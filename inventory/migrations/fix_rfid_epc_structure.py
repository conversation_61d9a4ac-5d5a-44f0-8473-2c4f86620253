from django.db import migrations

class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0022_alter_item__rfid_epc'),  # Adjust based on your latest applied migration
    ]

    operations = [
        migrations.RunSQL(
            """
            DO $$
            BEGIN
                -- Check if rfid_epc_old exists and drop it if it does
                IF EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_name = 'inventory_item' AND column_name = 'rfid_epc_old'
                ) THEN
                    ALTER TABLE inventory_item DROP COLUMN rfid_epc_old;
                END IF;
                
                -- Ensure rfid_epc exists with correct type
                IF NOT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_name = 'inventory_item' AND column_name = 'rfid_epc'
                ) THEN
                    ALTER TABLE inventory_item ADD COLUMN rfid_epc BYTEA;
                END IF;
            END $$;
            """,
            reverse_sql=migrations.RunSQL.noop
        ),
    ]